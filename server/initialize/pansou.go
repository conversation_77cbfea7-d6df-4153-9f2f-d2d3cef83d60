package initialize

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/config/pansou"
	pansouService "github.com/flipped-aurora/gin-vue-admin/server/service/pansou"
	pansouUtils "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou"
)

// 全局 PanSou 搜索服务实例
var PanSouSearchService *pansouService.SearchService

// API设置函数，用于避免循环导入
var apiSetterFunc func(*pansouService.SearchService)

// InitPanSou 初始化 PanSou 搜索服务
func InitPanSou() error {
	// 初始化 PanSou 配置
	pansou.Init()

	// 初始化 HTTP 客户端
	pansouUtils.InitHTTPClient(pansou.AppConfig)

	// 创建简单的插件管理器
	pluginManager := NewSimplePluginManager()

	// 初始化搜索服务
	searchService, err := pansouService.NewSearchService(pluginManager)
	if err != nil {
		return fmt.Errorf("初始化 PanSou 搜索服务失败: %v", err)
	}

		// 保存全局实例
	PanSouSearchService = searchService
	
	// 设置到API层的全局实例（避免循环导入）
	// 这里通过函数指针的方式在运行时设置，避免编译时循环依赖
	if apiSetterFunc != nil {
		apiSetterFunc(searchService)
	}
	
	fmt.Println("✅ PanSou 搜索服务初始化完成")
	return nil
}

// GetPanSouSearchService 获取 PanSou 搜索服务实例
func GetPanSouSearchService() *pansouService.SearchService {
	return PanSouSearchService
}

// SetAPISetterFunc 设置API设置函数，用于避免循环导入
func SetAPISetterFunc(setter func(*pansouService.SearchService)) {
	apiSetterFunc = setter
}

// 简单插件管理器实现
type SimplePluginManager struct {
	plugins []SimplePlugin
}

// SimplePlugin 简单插件接口实现
type SimplePlugin struct {
	name     string
	priority int
}

func (p *SimplePlugin) Name() string {
	return p.name
}

func (p *SimplePlugin) Priority() int {
	return p.priority
}

func (p *SimplePlugin) Search(keyword string, ext map[string]interface{}) ([]pansouService.PluginResult, error) {
	// 这是一个示例实现，实际应该调用具体的插件搜索逻辑
	// 在实际使用中，这里应该集成原有的异步插件系统
	return []pansouService.PluginResult{}, nil
}

// NewSimplePluginManager 创建简单插件管理器
func NewSimplePluginManager() *SimplePluginManager {
	pm := &SimplePluginManager{
		plugins: []SimplePlugin{
			{name: "demo_plugin", priority: 1},
		},
	}
	return pm
}

// GetPlugins 获取所有插件
func (pm *SimplePluginManager) GetPlugins() []pansouService.Plugin {
	var plugins []pansouService.Plugin
	for i := range pm.plugins {
		plugins = append(plugins, &pm.plugins[i])
	}
	return plugins
}
