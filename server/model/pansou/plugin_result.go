package pansou

import (
	"time"
)

// PluginResult 插件搜索结果
type PluginResult struct {
	Title    string            `json:"title"`
	URL      string            `json:"url"`
	Type     string            `json:"type"`     // 网盘类型：baidu、aliyun、quark等
	Password string            `json:"password"` // 提取码，可选
	Datetime time.Time         `json:"datetime"` // 发布时间
	Extra    map[string]string `json:"extra"`    // 额外信息，如文件大小、描述等
	UniqueID string            `json:"unique_id"` // 用于去重的唯一标识
	// 用于异步处理的字段
	Results   []PluginResult `json:"results,omitempty"`
	IsFinal   bool           `json:"is_final,omitempty"`
	Timestamp time.Time      `json:"timestamp,omitempty"`
	Source    string         `json:"source,omitempty"`
	Message   string         `json:"message,omitempty"`
}

// AsyncPluginResult 异步插件搜索结果（用于内部异步处理）
type AsyncPluginResult struct {
	Results   []PluginResult `json:"results"`
	IsFinal   bool           `json:"is_final"`
	Timestamp time.Time      `json:"timestamp"`
	Source    string         `json:"source"`
	Message   string         `json:"message"`
}
