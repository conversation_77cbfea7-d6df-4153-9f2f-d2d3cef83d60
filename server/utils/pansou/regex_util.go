package pansou

import (
	"regexp"
	"strings"
)

// 通用网盘链接匹配正则表达式 - 修改为更精确的匹配模式
var AllPanLinksPattern = regexp.MustCompile(`(?i)(?:(?:magnet:\?xt=urn:btih:[a-zA-Z0-9]+)|(?:ed2k://\|file\|[^|]+\|\d+\|[A-Fa-f0-9]+\|/?)|(?:https?://(?:(?:[\w.-]+\.)?(?:pan\.(?:baidu|quark)\.cn|(?:www\.)?(?:alipan|aliyundrive)\.com|drive\.uc\.cn|cloud\.189\.cn|caiyun\.139\.com|(?:www\.)?123(?:684|685|912|pan|592)\.(?:com|cn)|115\.com|115cdn\.com|anxia\.com|pan\.xunlei\.com|mypikpak\.com))(?:/[^\s'"<>()]*)?))`)

// 单独定义各种网盘的链接匹配模式，以便更精确地提取
// 修改百度网盘链接正则表达式，确保只匹配到链接本身，不包含后面的文本
var BaiduPanPattern = regexp.MustCompile(`https?://pan\.baidu\.com/s/[a-zA-Z0-9_-]+(?:\?pwd=[a-zA-Z0-9]{4})?`)
var QuarkPanPattern = regexp.MustCompile(`https?://pan\.quark\.cn/s/[a-zA-Z0-9]+`)
var XunleiPanPattern = regexp.MustCompile(`https?://pan\.xunlei\.com/s/[a-zA-Z0-9]+(?:\?pwd=[a-zA-Z0-9]+)?(?:#)?`)

// 添加天翼云盘链接正则表达式
var TianyiPanPattern = regexp.MustCompile(`https?://cloud\.189\.cn/t/[a-zA-Z0-9]+`)

// 添加UC网盘链接正则表达式
var UCPanPattern = regexp.MustCompile(`https?://drive\.uc\.cn/s/[a-zA-Z0-9]+(?:\?public=\d)?`)

// 添加123网盘链接正则表达式
var Pan123Pattern = regexp.MustCompile(`https?://(?:www\.)?123(?:684|685|912|pan|592)\.(?:com|cn)/s/[a-zA-Z0-9_-]+(?:\?(?:%E6%8F%90%E5%8F%96%E7%A0%81|提取码)[:：][a-zA-Z0-9]+)?`)

// 添加115网盘链接正则表达式
var Pan115Pattern = regexp.MustCompile(`https?://(?:115\.com|115cdn\.com|anxia\.com)/s/[a-zA-Z0-9]+(?:\?password=[a-zA-Z0-9]{4})?(?:#)?`)

// 添加阿里云盘链接正则表达式
var AliyunPanPattern = regexp.MustCompile(`https?://(?:www\.)?(?:alipan|aliyundrive)\.com/s/[a-zA-Z0-9]+`)

// 提取码匹配正则表达式 - 增强提取密码的能力
var PasswordPattern = regexp.MustCompile(`(?i)(?:(?:提取|访问|提取密|密)码|pwd)[：:]\s*([a-zA-Z0-9]{4})`)
var UrlPasswordPattern = regexp.MustCompile(`(?i)[?&]pwd=([a-zA-Z0-9]{4})`)

// 百度网盘密码专用正则表达式 - 确保只提取4位密码
var BaiduPasswordPattern = regexp.MustCompile(`(?i)(?:链接：.*?提取码：|密码：|提取码：|pwd=|pwd:|pwd：)([a-zA-Z0-9]{4})`)

// GetLinkType 获取链接类型
func GetLinkType(url string) string {
	url = strings.ToLower(url)

	// 处理可能带有"链接："前缀的情况
	if strings.Contains(url, "链接：") || strings.Contains(url, "链接:") {
		url = strings.Split(url, "链接")[1]
		if strings.HasPrefix(url, "：") || strings.HasPrefix(url, ":") {
			url = url[1:]
		}
		url = strings.TrimSpace(url)
	}

	// 根据关键词判断ed2k链接
	if strings.Contains(url, "ed2k:") {
		return "ed2k"
	}

	if strings.HasPrefix(url, "magnet:") {
		return "magnet"
	}

	if strings.Contains(url, "pan.baidu.com") {
		return "baidu"
	}
	if strings.Contains(url, "pan.quark.cn") {
		return "quark"
	}
	if strings.Contains(url, "alipan.com") || strings.Contains(url, "aliyundrive.com") {
		return "aliyun"
	}
	if strings.Contains(url, "cloud.189.cn") {
		return "tianyi"
	}
	if strings.Contains(url, "drive.uc.cn") {
		return "uc"
	}
	if strings.Contains(url, "caiyun.139.com") {
		return "mobile"
	}
	if strings.Contains(url, "115.com") || strings.Contains(url, "115cdn.com") || strings.Contains(url, "anxia.com") {
		return "115"
	}
	if strings.Contains(url, "mypikpak.com") {
		return "pikpak"
	}
	if strings.Contains(url, "pan.xunlei.com") {
		return "xunlei"
	}

	// 123网盘有多个域名
	if strings.Contains(url, "123684.com") || strings.Contains(url, "123685.com") ||
		strings.Contains(url, "123912.com") || strings.Contains(url, "123pan.com") ||
		strings.Contains(url, "123pan.cn") || strings.Contains(url, "123592.com") {
		return "123"
	}

	return "others"
}

// ExtractPassword 提取链接密码
func ExtractPassword(content, url string) string {
	// 先从URL中提取密码
	matches := UrlPasswordPattern.FindStringSubmatch(url)
	if len(matches) > 1 {
		// 确保百度网盘密码只有4位
		if strings.Contains(strings.ToLower(url), "pan.baidu.com") && len(matches[1]) > 4 {
			return matches[1][:4]
		}
		return matches[1]
	}

	// 特殊处理115网盘URL中的密码
	if (strings.Contains(url, "115.com") ||
		strings.Contains(url, "115cdn.com") ||
		strings.Contains(url, "anxia.com")) &&
		strings.Contains(url, "password=") {

		// 尝试从URL中提取密码
		passwordPattern := regexp.MustCompile(`password=([a-zA-Z0-9]{4})`)
		passwordMatches := passwordPattern.FindStringSubmatch(url)
		if len(passwordMatches) > 1 {
			return passwordMatches[1]
		}
	}

	// 特殊处理123网盘URL中的提取码
	if (strings.Contains(url, "123684.com") ||
		strings.Contains(url, "123685.com") ||
		strings.Contains(url, "123912.com") ||
		strings.Contains(url, "123pan.com") ||
		strings.Contains(url, "123pan.cn") ||
		strings.Contains(url, "123592.com")) &&
		(strings.Contains(url, "提取码") || strings.Contains(url, "%E6%8F%90%E5%8F%96%E7%A0%81")) {

		// 尝试从URL中提取提取码（处理普通文本和URL编码两种情况）
		extractCodePattern := regexp.MustCompile(`(?:提取码|%E6%8F%90%E5%8F%96%E7%A0%81)[:：]([a-zA-Z0-9]+)`)
		codeMatches := extractCodePattern.FindStringSubmatch(url)
		if len(codeMatches) > 1 {
			return codeMatches[1]
		}
	}

	// 对于百度网盘链接，尝试查找特定格式的密码
	if strings.Contains(strings.ToLower(url), "pan.baidu.com") {
		// 尝试匹配百度网盘特定格式的密码
		baiduMatches := BaiduPasswordPattern.FindStringSubmatch(content)
		if len(baiduMatches) > 1 {
			return baiduMatches[1]
		}
	}

	// 通用密码提取
	matches = PasswordPattern.FindStringSubmatch(content)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// 以下为清理URL的函数，省略详细实现，实际使用时需要完整的实现...
