package pansou

import (
	"fmt"
	"time"

	pansouModel "github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/cache"
	pansouJson "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/json"
)

// CacheWriteIntegration 缓存写入集成层
type CacheWriteIntegration struct {
	batchManager *cache.DelayedBatchWriteManager
	mainCache    *cache.EnhancedTwoLevelCache
	strategy     cache.CacheWriteStrategy
	initialized  bool
}

// NewCacheWriteIntegration 创建缓存写入集成
func NewCacheWriteIntegration(mainCache *cache.EnhancedTwoLevelCache) (*CacheWriteIntegration, error) {
	// 创建延迟批量写入管理器
	batchManager, err := cache.NewDelayedBatchWriteManager()
	if err != nil {
		return nil, fmt.Errorf("创建批量写入管理器失败: %v", err)
	}

	integration := &CacheWriteIntegration{
		batchManager: batchManager,
		mainCache:    mainCache,
	}

	// 设置主缓存更新函数
	batchManager.SetMainCacheUpdater(integration.createMainCacheUpdater())

	// 初始化管理器
	if err := batchManager.Initialize(); err != nil {
		return nil, fmt.Errorf("初始化批量写入管理器失败: %v", err)
	}

	integration.initialized = true

	fmt.Printf("✅ [缓存写入集成] 初始化完成\n")
	return integration, nil
}

// createMainCacheUpdater 创建主缓存更新函数
func (c *CacheWriteIntegration) createMainCacheUpdater() func(string, []byte, time.Duration) error {
	return func(key string, data []byte, ttl time.Duration) error {
		// 调用现有的缓存系统进行实际写入
		return c.mainCache.SetBothLevels(key, data, ttl)
	}
}

// WriteToCache 写入缓存
func (c *CacheWriteIntegration) WriteToCache(key string, data *pansouModel.SearchResponse, ttl time.Duration) error {
	if !c.initialized {
		return fmt.Errorf("缓存写入集成未初始化")
	}

	// 将SearchResponse转换为SearchResult数组
	var searchResults []pansouModel.SearchResult
	if data != nil {
		searchResults = data.Results
	}

	// 创建缓存操作
	op := &cache.CacheOperation{
		Key:       key,
		Data:      searchResults,
		TTL:       ttl,
		Timestamp: time.Now(),
		Priority:  2,
		DataSize:  len(searchResults) * 100, // 估算大小
		IsFinal:   true,
	}

	// 使用批量写入管理器
	return c.batchManager.HandleCacheOperation(op)
}

// ReadFromCache 从缓存读取
func (c *CacheWriteIntegration) ReadFromCache(key string) (*pansouModel.SearchResponse, bool, error) {
	if !c.initialized {
		return nil, false, fmt.Errorf("缓存写入集成未初始化")
	}

	// 从缓存读取
	data, found, err := c.mainCache.Get(key)
	if err != nil || !found {
		return nil, false, err
	}

	// data是[]byte类型，直接反序列化为SearchResponse
	var result pansouModel.SearchResponse
	err = pansouJson.Unmarshal(data, &result)
	if err != nil {
		return nil, false, fmt.Errorf("数据反序列化失败: %v", err)
	}

	return &result, true, nil
}

// Shutdown 关闭缓存写入集成
func (c *CacheWriteIntegration) Shutdown() error {
	if !c.initialized {
		return nil
	}

	// 关闭批量写入管理器
	return c.batchManager.Shutdown(10 * time.Second)
}
