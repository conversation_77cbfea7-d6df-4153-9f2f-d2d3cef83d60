package pansou

import (
	"context"
	"fmt"
	"sync"
	"time"

	pansouConfig "github.com/flipped-aurora/gin-vue-admin/server/config/pansou"
	pansouModel "github.com/flipped-aurora/gin-vue-admin/server/model/pansou"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/cache"
	pansouJson "github.com/flipped-aurora/gin-vue-admin/server/utils/pansou/json"
)

// 全局缓存写入管理器引用（避免循环依赖）
var globalCacheWriteManager *cache.DelayedBatchWriteManager

// SetGlobalCacheWriteManager 设置全局缓存写入管理器
func SetGlobalCacheWriteManager(manager *cache.DelayedBatchWriteManager) {
	globalCacheWriteManager = manager
}

// GetGlobalCacheWriteManager 获取全局缓存写入管理器
func GetGlobalCacheWriteManager() *cache.DelayedBatchWriteManager {
	return globalCacheWriteManager
}

// GetEnhancedTwoLevelCache 获取增强版两级缓存实例
func GetEnhancedTwoLevelCache() *cache.EnhancedTwoLevelCache {
	return enhancedTwoLevelCache
}

// 全局缓存实例和缓存是否初始化标志
var (
	enhancedTwoLevelCache *cache.EnhancedTwoLevelCache
	cacheInitialized      bool
)

// SearchService 搜索服务
type SearchService struct {
	pluginManager    PluginManager
	cacheIntegration *CacheWriteIntegration
	mutex            sync.RWMutex
}

// PluginManager 插件管理器接口
type PluginManager interface {
	GetPlugins() []Plugin
}

// Plugin 插件接口
type Plugin interface {
	Name() string
	Priority() int
	Search(keyword string, ext map[string]interface{}) ([]PluginResult, error)
}

// PluginResult 插件搜索结果
type PluginResult struct {
	Title    string            `json:"title"`
	URL      string            `json:"url"`
	Type     string            `json:"type"`
	Password string            `json:"password"`
	Datetime time.Time         `json:"datetime"`
	Extra    map[string]string `json:"extra"`
}

// NewSearchService 创建搜索服务
func NewSearchService(pluginManager PluginManager) (*SearchService, error) {
	// 初始化缓存
	var cacheIntegration *CacheWriteIntegration
	if pansouConfig.AppConfig != nil && pansouConfig.AppConfig.CacheEnabled {
		enhancedCache, err := cache.NewEnhancedTwoLevelCacheWithConfig(pansouConfig.AppConfig.CachePath, pansouConfig.AppConfig.CacheMaxSizeMB)
		if err != nil {
			return nil, fmt.Errorf("初始化缓存失败: %v", err)
		}
		enhancedTwoLevelCache = enhancedCache
		cacheInitialized = true

		integration, err := NewCacheWriteIntegration(enhancedCache)
		if err != nil {
			return nil, fmt.Errorf("初始化缓存集成失败: %v", err)
		}
		cacheIntegration = integration
	}

	return &SearchService{
		pluginManager:    pluginManager,
		cacheIntegration: cacheIntegration,
	}, nil
}

// Search 执行搜索
func (s *SearchService) Search(
	keyword string,
	channels []string,
	concurrency int,
	forceRefresh bool,
	resultType string,
	sourceType string,
	plugins []string,
	cloudTypes []string,
	ext map[string]interface{},
) (*pansouModel.SearchResponse, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 生成缓存键
	cacheKey := generateCacheKey(keyword, channels, plugins, cloudTypes, ext)

	// 检查缓存（如果不是强制刷新）
	if !forceRefresh && s.cacheIntegration != nil {
		if cachedResult, found, err := s.cacheIntegration.ReadFromCache(cacheKey); err == nil && found {
			fmt.Printf("🎯 缓存命中: %s\n", keyword)
			return cachedResult, nil
		}
	}

	// 执行实际搜索
	searchResults, err := s.performSearch(keyword, channels, plugins, sourceType, ext, concurrency)
	if err != nil {
		return nil, fmt.Errorf("搜索执行失败: %v", err)
	}

	// 过滤云盘类型
	if len(cloudTypes) > 0 {
		searchResults = s.filterByCloudTypes(searchResults, cloudTypes)
	}

	// 创建响应
	response := &pansouModel.SearchResponse{
		Total:   len(searchResults),
		Results: searchResults,
	}

	// 根据结果类型处理响应
	switch resultType {
	case "merged_by_type":
		response.MergedByType = s.mergeResultsByType(searchResults)
		response.Results = nil
	case "all":
		response.MergedByType = s.mergeResultsByType(searchResults)
	case "results":
		// 默认已经设置了Results
	}

	// 缓存结果
	if s.cacheIntegration != nil && pansouConfig.AppConfig != nil {
		ttl := time.Duration(pansouConfig.AppConfig.CacheTTLMinutes) * time.Minute
		if err := s.cacheIntegration.WriteToCache(cacheKey, response, ttl); err != nil {
			fmt.Printf("⚠️ 缓存写入失败: %v\n", err)
		}
	}

	return response, nil
}

// performSearch 执行实际搜索
func (s *SearchService) performSearch(
	keyword string,
	channels []string,
	plugins []string,
	sourceType string,
	ext map[string]interface{},
	concurrency int,
) ([]pansouModel.SearchResult, error) {
	var results []pansouModel.SearchResult
	var mu sync.Mutex

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 创建工作组
	var wg sync.WaitGroup

	// 搜索插件（如果启用）
	if sourceType == "all" || sourceType == "plugin" {
		if s.pluginManager != nil {
			availablePlugins := s.pluginManager.GetPlugins()

			for _, plugin := range availablePlugins {
				// 如果指定了插件列表，只搜索指定的插件
				if len(plugins) > 0 && !contains(plugins, plugin.Name()) {
					continue
				}

				wg.Add(1)
				go func(p Plugin) {
					defer wg.Done()

					select {
					case <-ctx.Done():
						return
					default:
						pluginResults, err := p.Search(keyword, ext)
						if err != nil {
							fmt.Printf("⚠️ 插件 %s 搜索失败: %v\n", p.Name(), err)
							return
						}

						// 转换插件结果为搜索结果
						converted := s.convertPluginResults(pluginResults, p.Name())

						mu.Lock()
						results = append(results, converted...)
						mu.Unlock()
					}
				}(plugin)
			}
		}
	}

	// TODO: 添加 Telegram 频道搜索（如果启用）
	if sourceType == "all" || sourceType == "tg" {
		// 这里可以添加 Telegram 频道搜索逻辑
		// 由于原项目的 TG 搜索逻辑较复杂，这里暂时省略
	}

	// 等待所有搜索完成
	wg.Wait()

	return results, nil
}

// convertPluginResults 转换插件结果为搜索结果
func (s *SearchService) convertPluginResults(pluginResults []PluginResult, pluginName string) []pansouModel.SearchResult {
	var results []pansouModel.SearchResult

	for i, pr := range pluginResults {
		result := pansouModel.SearchResult{
			MessageID: fmt.Sprintf("plugin_%s_%d", pluginName, i),
			UniqueID:  fmt.Sprintf("plugin_%s_%d_%d", pluginName, time.Now().Unix(), i),
			Channel:   "plugin:" + pluginName,
			Datetime:  pr.Datetime,
			Title:     pr.Title,
			Content:   pr.Title,
			Links: []pansouModel.Link{
				{
					Type:     pr.Type,
					URL:      pr.URL,
					Password: pr.Password,
				},
			},
			Tags:   []string{},
			Images: []string{},
		}
		results = append(results, result)
	}

	return results
}

// filterByCloudTypes 按云盘类型过滤结果
func (s *SearchService) filterByCloudTypes(results []pansouModel.SearchResult, cloudTypes []string) []pansouModel.SearchResult {
	if len(cloudTypes) == 0 {
		return results
	}

	var filtered []pansouModel.SearchResult
	cloudTypeSet := make(map[string]bool)
	for _, ct := range cloudTypes {
		cloudTypeSet[ct] = true
	}

	for _, result := range results {
		var hasValidLink bool
		var validLinks []pansouModel.Link

		for _, link := range result.Links {
			if cloudTypeSet[link.Type] {
				validLinks = append(validLinks, link)
				hasValidLink = true
			}
		}

		if hasValidLink {
			result.Links = validLinks
			filtered = append(filtered, result)
		}
	}

	return filtered
}

// mergeResultsByType 按类型合并结果
func (s *SearchService) mergeResultsByType(results []pansouModel.SearchResult) pansouModel.MergedLinks {
	merged := make(pansouModel.MergedLinks)

	for _, result := range results {
		for _, link := range result.Links {
			merged[link.Type] = append(merged[link.Type], pansouModel.MergedLink{
				URL:      link.URL,
				Password: link.Password,
				Note:     result.Title,
				Datetime: result.Datetime,
				Source:   result.Channel,
				Images:   result.Images,
			})
		}
	}

	return merged
}

// generateCacheKey 生成缓存键
func generateCacheKey(keyword string, channels []string, plugins []string, cloudTypes []string, ext map[string]interface{}) string {
	data := map[string]interface{}{
		"keyword":    keyword,
		"channels":   channels,
		"plugins":    plugins,
		"cloudTypes": cloudTypes,
		"ext":        ext,
	}

	jsonData, _ := pansouJson.Marshal(data)
	return fmt.Sprintf("search_%x", jsonData)[:32] // 限制长度
}

// contains 检查slice是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// GetPluginManager 获取插件管理器
func (s *SearchService) GetPluginManager() PluginManager {
	return s.pluginManager
}

// Shutdown 关闭搜索服务
func (s *SearchService) Shutdown() error {
	if s.cacheIntegration != nil {
		return s.cacheIntegration.Shutdown()
	}
	return nil
}
